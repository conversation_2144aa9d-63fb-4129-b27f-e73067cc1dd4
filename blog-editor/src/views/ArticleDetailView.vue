<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'

// Dummy article data
const article = ref({
  id: '1',
  title: 'Getting Started with Vue 3',
  content: 'This is a sample article about Vue 3. It covers the basics of the framework and how to get started with building modern web applications.'
    const doubled = computed(() => count.value * 2)
    
    return { count, doubled }
  }
})
\`\`\`

### 2. Better Performance
Vue 3 is significantly faster than Vue 2, with improvements in:
- Virtual DOM diffing algorithm
- Component initialization
- Memory usage

### 3. TypeScript Support
Vue 3 has first-class TypeScript support with better type inference and improved developer experience.

## Setting Up a Vue 3 Project

### Using Create Vue App

\`\`\`bash
npm create vue@latest
\`\`\`

This will create a new Vue 3 project with a modern build setup.

### Manual Setup

You can also set up Vue 3 manually:

\`\`\`html
<!DOCTYPE html>
<html>
<head>
  <title>Vue 3 App</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
  <div id="app">
    {{ message }}
  </div>

  <script>
    const { createApp } = Vue

    createApp({
      data() {
        return {
          message: 'Hello Vue 3!'
        }
      }
    }).mount('#app')
  </script>
</body>
</html>
\`\`\`

## Basic Concepts

### Templates

Vue 3 uses HTML-based templates with special syntax:

\`\`\`html
<template>
  <div>
    <h1>{{ title }}</h1>
    <button @click="increment">Count: {{ count }}</button>
  </div>
</template>
\`\`\`

### Components

Components are the building blocks of Vue applications:

\`\`\`javascript
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'MyComponent',
  props: {
    title: String
  },
  setup(props) {
    // Component logic
    return {
      // Template bindings
    }
  }
})
\`\`\`

### Lifecycle Hooks

Vue 3 provides lifecycle hooks for different stages of a component's life:

\`\`\`javascript
import { onMounted, onUpdated, onUnmounted } from 'vue'

export default {
  setup() {
    onMounted(() => {
      console.log('Component mounted')
    })
    
    onUpdated(() => {
      console.log('Component updated')
    })
    
    onUnmounted(() => {
      console.log('Component unmounted')
    })
  }
}
\`\`\`

## Advanced Features

### Composables

Composables are reusable function-based APIs that can be used across components:

\`\`\`javascript
// useCounter.js
import { ref } from 'vue'

export function useCounter() {
  const count = ref(0)
  const increment = () => count.value++
  const decrement = () => count.value--
  
  return { count, increment, decrement }
}
\`\`\`

### Teleport

Teleport allows you to render components in a different part of the DOM:

\`\`\`html
<template>
  <button @click="showModal = true">Open Modal</button>
  
  <Teleport to="body">
    <div v-if="showModal" class="modal">
      Modal content
    </div>
  </Teleport>
</template>
\`\`\`

### Suspense

Suspense helps handle async components and loading states:

\`\`\`html
<Suspense>
  <template #default>
    <AsyncComponent />
  </template>
  <template #fallback>
    <div>Loading...</div>
  </template>
</Suspense>
\`\`\`

## Best Practices

1. **Use Composition API for complex components**: It provides better code organization
2. **Leverage TypeScript**: For better type safety and developer experience
3. **Keep components focused**: Each component should have a single responsibility
4. **Use computed properties**: For derived data that depends on reactive data
5. **Embrace reactivity**: Use Vue's reactivity system effectively

## Conclusion

Vue 3 is a powerful and modern framework that builds upon the strengths of Vue 2 while introducing new features that make development more enjoyable and productive. Whether you're new to Vue or migrating from Vue 2, Vue 3 offers a great experience for building modern web applications.

Happy coding with Vue 3!
  `,
  excerpt: 'Learn the basics of Vue 3 and modern web development with this comprehensive guide.',
  author: 'John Doe',
  date: '2024-01-15',
  readTime: '5 min read',
  tags: ['Vue', 'JavaScript', 'Tutorial'],
  views: 1250,
  likes: 89,
  status: 'published',
  category: 'Web Development'
})

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">{{ article.title }}</h1>
        <p class="text-muted-foreground">{{ article.excerpt }}</p>
      </div>
      <div class="flex gap-2">
        <Button variant="outline">Edit</Button>
        <Button variant="outline">Delete</Button>
      </div>
    </div>

    <!-- Article Meta -->
    <Card>
      <CardContent class="pt-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <Avatar>
              <AvatarFallback>{{ article.author.split(' ').map(n => n[0]).join('') }}</AvatarFallback>
            </Avatar>
            <div>
              <p class="font-medium">{{ article.author }}</p>
              <p class="text-muted-foreground text-sm">{{ formatDate(article.date) }}</p>
            </div>
          </div>
          <div class="text-right">
            <p class="text-muted-foreground text-sm">{{ article.readTime }}</p>
            <p class="text-muted-foreground text-sm">{{ article.views }} views</p>
          </div>
        </div>
        
        <Separator class="my-4" />
        
        <div class="flex flex-wrap gap-2">
          <Badge v-for="tag in article.tags" :key="tag" variant="secondary">
            {{ tag }}
          </Badge>
        </div>
      </CardContent>
    </Card>

    <!-- Article Content -->
    <Card>
      <CardContent class="pt-6">
        <div 
          class="max-w-none prose prose-lg"
          :class="{ 'prose-invert': document.documentElement.classList.contains('dark') }"
          v-html="article.content.replace(/\n/g, '<br>')"
        ></div>
      </CardContent>
    </Card>

    <!-- Article Actions -->
    <Card>
      <CardContent class="pt-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <Button variant="outline" class="flex items-center gap-2">
              <span>👍</span> Like ({{ article.likes }})
            </Button>
            <Button variant="outline" class="flex items-center gap-2">
              <span>💬</span> Comment
            </Button>
            <Button variant="outline" class="flex items-center gap-2">
              <span>🔗</span> Share
            </Button>
          </div>
          <div class="flex items-center gap-2">
            <Button variant="outline">
              <span>📧</span> Email
            </Button>
            <Button variant="outline">
              <span>📋</span> Copy Link
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<style scoped>
.prose {
  line-height: 1.7;
}

.prose h1 {
  font-size: 2.5em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.prose h2 {
  font-size: 2em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.prose h3 {
  font-size: 1.5em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.prose p {
  margin-bottom: 1em;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-family: 'Fira Code', monospace;
}

.prose pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
}

.prose blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
}

.prose ul, .prose ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}

.prose li {
  margin-bottom: 0.5em;
}

.prose-inverse h1,
.prose-inverse h2,
.prose-inverse h3,
.prose-inverse p,
.prose-inverse li {
  color: #e5e7eb;
}

.prose-inverse code {
  background-color: #374151;
  color: #f9fafb;
}

.prose-inverse pre {
  background-color: #111827;
}
</style>