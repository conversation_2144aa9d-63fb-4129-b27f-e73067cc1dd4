<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import {
  TagsInput,
  TagsInputInput,
  TagsInputItem,
  TagsInputItemDelete,
  TagsInputItemText,
} from '@/components/ui/tags-input'
import type { DateValue } from "@internationalized/date"
import type { Ref } from "vue"
import { getLocalTimeZone, now } from "@internationalized/date"
import { Calendar } from "@/components/ui/calendar"
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

import MarkdownPreview from '@/components/MarkdownPreview.vue'
import DraftManager from '@/components/DraftManager.vue'
import { generateUniqueSlug, generateBaseSlug, createDebouncedSlugValidator, type SlugValidationResult } from '@/lib/slug-utils'
import { createArticle, getTags } from '@/lib/supabase'
import { useAuth } from '@/composables/useAuth'
import { useDrafts } from '@/composables/useDrafts'
import { toast } from 'vue-sonner'

const router = useRouter()
const { profile: currentProfile } = useAuth()
const {
  autoSaveDraft,
  clearCurrentDraft,
  getLatestDraft,
  loadDraft,
  hasDrafts
} = useDrafts()

// Article data
const title = ref('')
const content = ref('')
const excerpt = ref('')
const tags = ref<string[]>([])
const publishDate = ref(now(getLocalTimeZone())) as Ref<DateValue>

// Editor state
const isPreviewMode = ref(false)
const autoSaveInterval = ref<NodeJS.Timeout | null>(null)
const lastSaved = ref<Date | null>(null)
const isDirty = ref(false)

// Slug state
const customSlug = ref('')
const isSlugCustomized = ref(false)
const slugValidation = ref<SlugValidationResult>({ isValid: true, isUnique: true })
const isValidatingSlug = ref(false)

// Supabase state
const availableTags = ref<any[]>([])
const isLoading = ref(false)
const isSaving = ref(false)
const isPublishing = ref(false)



// Slug management
const slug = computed(() => {
  if (isSlugCustomized.value && customSlug.value) {
    return customSlug.value
  }
  return generateBaseSlug(title.value)
})

// Create debounced slug validator
const debouncedSlugValidator = createDebouncedSlugValidator((result) => {
  slugValidation.value = result
  isValidatingSlug.value = false
})

// Auto-generate slug from title
const generateSlugFromTitle = async () => {
  if (!title.value || isSlugCustomized.value) return

  try {
    const uniqueSlug = await generateUniqueSlug(title.value)
    customSlug.value = uniqueSlug
  } catch (error) {
    console.error('Error generating slug:', error)
  }
}

// Validate custom slug
const validateCustomSlug = async (slugValue: string) => {
  if (!slugValue) return

  isValidatingSlug.value = true
  debouncedSlugValidator(slugValue)
}

// Handle slug input
const handleSlugInput = (value: string) => {
  customSlug.value = value
  isSlugCustomized.value = true
  validateCustomSlug(value)
}

// Use suggested slug
const useSuggestedSlug = (suggestion: string) => {
  customSlug.value = suggestion
  isSlugCustomized.value = true
  validateCustomSlug(suggestion)
}

// Add tag to the list
const addTag = (tagName: string) => {
  if (!tags.value.includes(tagName)) {
    tags.value.push(tagName)
  }
}



// Markdown rendering is now handled by MarkdownPreview component

// Auto-save functionality using new draft system
const saveToLocalStorage = () => {
  const draftData = {
    title: title.value,
    content: content.value,
    excerpt: excerpt.value,
    tags: tags.value,
    publishDate: publishDate.value.toString()
  }

  const savedDraft = autoSaveDraft(draftData)
  if (savedDraft) {
    lastSaved.value = new Date()
    isDirty.value = false
  }
}

// Load latest draft automatically on component mount
const loadLatestDraft = () => {
  const latestDraft = getLatestDraft()
  if (latestDraft) {
    loadDraftData(latestDraft)
  }
}

// Load draft data into form
const loadDraftData = (draft: any) => {
  title.value = draft.title || ''
  content.value = draft.content || ''
  excerpt.value = draft.excerpt || ''
  tags.value = draft.tags || []
  if (draft.publishDate) {
    try {
      // Handle date parsing
      publishDate.value = draft.publishDate
    } catch (error) {
      console.error('Error parsing publish date:', error)
    }
  }
  if (draft.lastSaved) {
    lastSaved.value = new Date(draft.lastSaved)
  }
  isDirty.value = false
}

// Handle draft loaded from DraftManager
const onDraftLoaded = (draft: any) => {
  loadDraftData(draft)
}

// Watch for changes to mark as dirty
watch([title, content, excerpt, tags, categories, customSlug], () => {
  isDirty.value = true
}, { deep: true })

// Watch title changes for auto-slug generation
watch(title, (newTitle) => {
  if (newTitle && !isSlugCustomized.value) {
    generateSlugFromTitle()
  }
})

// Watch custom slug for validation
watch(customSlug, (newSlug) => {
  if (newSlug && isSlugCustomized.value) {
    validateCustomSlug(newSlug)
  }
})

// Load initial data
const loadInitialData = async () => {
  try {
    isLoading.value = true

    // Load available tags
    const tags = await getTags()
    availableTags.value = tags

  } catch (error: any) {
    console.error('Error loading initial data:', error)
    // Handle authentication error - redirect to login
    if (error.message?.includes('not authenticated')) {
      router.push('/login')
    }
  } finally {
    isLoading.value = false
  }
}

// Auto-save every 30 seconds
onMounted(async () => {
  await loadInitialData()
  loadLatestDraft() // Load latest draft automatically

  // Add keyboard shortcuts
  document.addEventListener('keydown', handleKeyboardShortcuts)

  autoSaveInterval.value = setInterval(() => {
    if (isDirty.value && (title.value || content.value)) {
      saveToLocalStorage()
    }
  }, 30000) // 30 seconds
})

// Keyboard shortcuts
const handleKeyboardShortcuts = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 's':
        event.preventDefault()
        saveArticle()
        break
      case 'Enter':
        event.preventDefault()
        publishArticle()
        break
      case '/':
        event.preventDefault()
        isPreviewMode.value = !isPreviewMode.value
        break
    }
  }
}

// This duplicate onMounted was removed

onUnmounted(() => {
  // Remove keyboard shortcuts
  document.removeEventListener('keydown', handleKeyboardShortcuts)

  if (autoSaveInterval.value) {
    clearInterval(autoSaveInterval.value)
  }
  // Save on unmount if dirty
  if (isDirty.value && (title.value || content.value)) {
    saveToLocalStorage()
  }
})

const saveArticle = async () => {
  if (!title.value.trim()) {
    toast.error('Please enter a title for your article')
    return
  }

  // Ensure we have a valid slug
  if (!slug.value || slug.value.trim() === '') {
    toast.error('Please wait for slug generation to complete')
    return
  }

  try {
    isSaving.value = true

    const articleData = {
      title: title.value.trim(),
      slug: slug.value.trim(),
      content: content.value,
      excerpt: excerpt.value.trim() || null,
      status: 'draft' as const,
      meta_title: title.value.trim(),
      meta_description: excerpt.value.trim() || null
    }

    await createArticle(articleData, tags.value)

    // Clear current draft after successful save
    clearCurrentDraft()
    isDirty.value = false

    toast.success('Draft saved successfully!', {
      description: 'Your article has been saved as a draft'
    })

  } catch (error: any) {
    console.error('Error saving article:', error)
    toast.error('Failed to save article', {
      description: error.message || 'Please try again'
    })
  } finally {
    isSaving.value = false
  }
}

const publishArticle = async () => {
  if (!title.value.trim()) {
    toast.error('Please enter a title for your article')
    return
  }

  if (!content.value.trim()) {
    toast.error('Please add some content to your article')
    return
  }

  // Ensure we have a valid slug
  if (!slug.value || slug.value.trim() === '') {
    toast.error('Please wait for slug generation to complete')
    return
  }

  try {
    isPublishing.value = true

    const articleData = {
      title: title.value.trim(),
      slug: slug.value.trim(),
      content: content.value,
      excerpt: excerpt.value.trim() || null,
      status: 'published' as const,
      published_at: new Date().toISOString(),
      meta_title: title.value.trim(),
      meta_description: excerpt.value.trim() || null
    }

    await createArticle(articleData, tags.value)

    // Clear current draft after successful publish
    clearCurrentDraft()
    isDirty.value = false

    toast.success('Article published successfully!', {
      description: 'Your article is now live and visible to readers',
      action: {
        label: 'View Articles',
        onClick: () => router.push('/articles')
      }
    })

    // Redirect to articles list after a short delay
    setTimeout(() => {
      router.push('/articles')
    }, 2000)

  } catch (error: any) {
    console.error('Error publishing article:', error)
    toast.error('Failed to publish article', {
      description: error.message || 'Please try again'
    })
  } finally {
    isPublishing.value = false
  }
}
</script>

<template>
  <div v-if="isLoading" class="p-6 flex items-center justify-center min-h-[400px]">
    <div class="space-y-4 text-center">
      <div class="border-primary border-t-transparent animate-spin w-8 h-8 mx-auto border-4 rounded-full"></div>
      <p class="text-muted-foreground">Loading editor...</p>
    </div>
  </div>

  <div v-else class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">New Article</h1>
        <p class="text-muted-foreground">Create a new blog post</p>
        <div class="flex items-center gap-4 mt-2">
          <Badge v-if="isDirty" variant="secondary" class="text-xs">
            Unsaved changes
          </Badge>
          <span v-if="lastSaved" class="text-muted-foreground text-xs">
            Last saved: {{ lastSaved.toLocaleTimeString() }}
          </span>
          <span v-if="slug" class="text-muted-foreground text-xs">
            Slug: {{ slug }}
          </span>
        </div>
      </div>
      <div class="flex gap-2">
        <Button
          variant="outline"
          @click="saveArticle"
          :disabled="isSaving || isLoading"
        >
          <div v-if="isSaving" class="border-t-transparent animate-spin w-4 h-4 mr-2 border-2 border-current rounded-full"></div>
          {{ isSaving ? 'Saving...' : 'Save Draft' }}
        </Button>
        <Button
          @click="publishArticle"
          :disabled="isPublishing || isLoading || isSaving"
        >
          <div v-if="isPublishing" class="border-t-transparent animate-spin w-4 h-4 mr-2 border-2 border-current rounded-full"></div>
          {{ isPublishing ? 'Publishing...' : 'Publish' }}
        </Button>
      </div>
    </div>

    <!-- Draft Manager -->
    <Card v-if="hasDrafts" class="mb-6">
      <CardHeader>
        <CardTitle class="text-lg">Draft Manager</CardTitle>
      </CardHeader>
      <CardContent>
        <DraftManager @draft-loaded="onDraftLoaded" />
      </CardContent>
    </Card>

    <div class="lg:grid-cols-3 grid grid-cols-1 gap-6">
      <!-- Editor -->
      <div class="lg:col-span-2">
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>Content Editor</CardTitle>
              <div class="flex gap-2">
              
              </div>
            </div>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <Label for="title">Title</Label>
              <Input
                id="title"
                v-model="title"
                placeholder="Enter article title..."
                class="text-lg"
              />
            </div>

            <div class="space-y-2">
              <Label for="excerpt">Excerpt</Label>
              <Input
                id="excerpt"
                v-model="excerpt"
                placeholder="Brief description of your article..."
              />
            </div>

            <!-- Split View Editor -->
            <div class="space-y-2">
              <Label for="content">Content</Label>
              <div class="overflow-hidden border rounded-lg">
                <Tabs :default-value="isPreviewMode ? 'preview' : 'edit'" class="w-full">
                  <TabsList class="grid w-full grid-cols-3">
                    <TabsTrigger value="edit">Edit</TabsTrigger>
                    <TabsTrigger value="preview">Preview</TabsTrigger>
                    <TabsTrigger value="split">Split View</TabsTrigger>
                  </TabsList>

                  <TabsContent value="edit" class="mt-0">
                    <Textarea
                      id="content"
                      v-model="content"
                      placeholder="Start writing your article in markdown..."
                      class="min-h-[500px] border-0 resize-none focus:ring-0"
                    />
                  </TabsContent>

                  <TabsContent value="preview" class="mt-0">
                    <div class="min-h-[500px] p-4 prose prose-sm max-w-none dark:prose-invert overflow-auto">
                      <MarkdownPreview :content="content" />
                    </div>
                  </TabsContent>

                  <TabsContent value="split" class="mt-0">
                    <div class="grid grid-cols-2 gap-0 min-h-[500px]">
                      <div class="border-r">
                        <Textarea
                          v-model="content"
                          placeholder="Start writing your article in markdown..."
                          class="focus:ring-0 h-full border-0 resize-none"
                        />
                      </div>
                      <div class="max-w-none dark:prose-invert bg-muted/30 p-4 overflow-auto prose-sm prose">
                        <MarkdownPreview :content="content" />
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
              <p class="text-muted-foreground text-xs">
                💡 Tip: Use markdown syntax for formatting. Supports headings, lists, code blocks, links, and custom components.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Author Info -->
        <Card v-if="currentProfile">
          <CardHeader>
            <CardTitle>Author</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="flex items-center gap-3">
              <Avatar class="w-10 h-10">
                <AvatarImage :src="currentProfile?.avatar_url || ''" />
                <AvatarFallback>
                  {{ currentProfile.full_name?.charAt(0) || currentProfile.username?.charAt(0) || 'U' }}
                </AvatarFallback>
              </Avatar>
              <div>
                <p class="font-medium">{{ currentProfile.full_name || currentProfile.username }}</p>
                <Badge variant="secondary" class="text-xs">{{ currentProfile.role }}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Article Settings -->
        <Card>
          <CardHeader>
            <CardTitle>Settings</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <Label for="slug">URL Slug</Label>
              <Input
                id="slug"
                :value="slug"
                @input="handleSlugInput($event.target.value)"
                placeholder="auto-generated-from-title"
                class="font-mono text-sm"
              />
              <div class="flex items-center gap-2 text-xs">
                <div v-if="isValidatingSlug" class="text-muted-foreground flex items-center gap-1">
                  <div class="border-t-transparent animate-spin w-3 h-3 border-2 border-current rounded-full"></div>
                  Checking availability...
                </div>
                <div v-else-if="!slugValidation.isValid" class="text-red-500">
                  {{ slugValidation.error }}
                </div>
                <div v-else-if="!slugValidation.isUnique" class="text-orange-500">
                  {{ slugValidation.error }}
                </div>
                <div v-else-if="slug" class="text-green-500">
                  ✓ Available
                </div>
              </div>

              <!-- Slug suggestions -->
              <div v-if="slugValidation.suggestions && slugValidation.suggestions.length > 0" class="space-y-2">
                <p class="text-muted-foreground text-xs">Suggestions:</p>
                <div class="flex flex-wrap gap-1">
                  <Button
                    v-for="suggestion in slugValidation.suggestions"
                    :key="suggestion"
                    variant="outline"
                    size="sm"
                    class="h-6 px-2 text-xs"
                    @click="useSuggestedSlug(suggestion)"
                  >
                    {{ suggestion }}
                  </Button>
                </div>
              </div>
            </div>



            <div class="space-y-2">
              <Label for="publishDate">Publish Date</Label>
              <Calendar v-model="publishDate" :weekday-format="'short'" class="border rounded-md" />
            </div>

            <Separator />

            <div class="space-y-2">
              <Label>Tags</Label>
              <TagsInput v-model="tags">
                <TagsInputItem v-for="item in tags" :key="item" :value="item">
                  <TagsInputItemText />
                  <TagsInputItemDelete />
                </TagsInputItem>

                <TagsInputInput placeholder="Add tags..." />
              </TagsInput>

              <!-- Tag suggestions -->
              <div v-if="availableTags.length > 0" class="space-y-2">
                <p class="text-muted-foreground text-xs">Popular tags:</p>
                <div class="flex flex-wrap gap-1">
                  <Button
                    v-for="tag in availableTags.slice(0, 8)"
                    :key="tag.id"
                    variant="outline"
                    size="sm"
                    class="h-6 px-2 text-xs"
                    @click="addTag(tag.name)"
                    :disabled="tags.includes(tag.name)"
                  >
                    {{ tag.name }}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Article Info -->
        <Card>
          <CardHeader>
            <CardTitle>Article Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div>
                <h3 class="line-clamp-2 font-semibold">{{ title || 'Untitled Article' }}</h3>
                <p class="text-muted-foreground line-clamp-3 text-sm">{{ excerpt || 'No excerpt provided' }}</p>
              </div>
              <Separator />

              <!-- URL Preview -->
              <div class="space-y-1">
                <p class="text-xs font-medium">URL Preview</p>
                <code class="bg-muted block px-2 py-1 text-xs break-all rounded">
                  /{{ slug || 'auto-generated' }}
                </code>
                <div v-if="slug" class="flex items-center gap-1">
                  <Badge v-if="!slugValidation.isValid" variant="destructive" class="text-xs">
                    Invalid
                  </Badge>
                  <Badge v-else-if="!slugValidation.isUnique" variant="secondary" class="text-xs">
                    Taken
                  </Badge>
                  <Badge v-else variant="default" class="text-xs bg-green-500">
                    Available
                  </Badge>
                </div>
              </div>

              <Separator />

              <!-- Content Stats -->
              <div class="space-y-2">
                <p class="text-xs font-medium">Content Statistics</p>
                <div class="grid grid-cols-2 gap-2 text-xs">
                  <div class="bg-muted/50 p-2 rounded">
                    <p class="font-medium">{{ content.split(/\s+/).filter(w => w.length > 0).length }}</p>
                    <p class="text-muted-foreground">Words</p>
                  </div>
                  <div class="bg-muted/50 p-2 rounded">
                    <p class="font-medium">{{ content.length }}</p>
                    <p class="text-muted-foreground">Characters</p>
                  </div>
                  <div class="bg-muted/50 p-2 rounded">
                    <p class="font-medium">{{ Math.ceil(content.split(/\s+/).filter(w => w.length > 0).length / 200) }}</p>
                    <p class="text-muted-foreground">Min read</p>
                  </div>
                  <div class="bg-muted/50 p-2 rounded">
                    <p class="font-medium">{{ tags.length }}</p>
                    <p class="text-muted-foreground">Tags</p>
                  </div>
                </div>
              </div>

              <!-- Metadata -->
              <div class="text-muted-foreground space-y-1 text-xs">
                <p><strong>Categories:</strong> {{ categories.length > 0 ? categories.join(', ') : 'No categories' }}</p>
                <p><strong>Tags:</strong> {{ tags.length > 0 ? tags.join(', ') : 'No tags' }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Quick Preview -->
        <Card>
          <CardHeader>
            <CardTitle>Quick Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="prose-xs max-w-none dark:prose-invert max-h-48 overflow-auto text-xs prose">
              <MarkdownPreview :content="content" />
            </div>
          </CardContent>
        </Card>

        <!-- Markdown Help -->
        <Card>
          <CardHeader>
            <CardTitle>Markdown Guide</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-2 text-xs">
              <div class="grid grid-cols-2 gap-2">
                <div>
                  <code class="bg-muted px-1 rounded"># Heading</code>
                  <p class="text-muted-foreground">Large heading</p>
                </div>
                <div>
                  <code class="bg-muted px-1 rounded">**bold**</code>
                  <p class="text-muted-foreground">Bold text</p>
                </div>
                <div>
                  <code class="bg-muted px-1 rounded">*italic*</code>
                  <p class="text-muted-foreground">Italic text</p>
                </div>
                <div>
                  <code class="bg-muted px-1 rounded">`code`</code>
                  <p class="text-muted-foreground">Inline code</p>
                </div>
              </div>
              <Separator />
              <div class="space-y-1">
                <p class="font-medium">Code Blocks:</p>
                <code class="bg-muted block px-1 text-xs rounded">
                  ```dart<br/>
                  void main() {<br/>
                  &nbsp;&nbsp;print('Hello Flutter!');<br/>
                  }
                </code>
                <p class="text-muted-foreground">Dart, JS, TS, Python, and 20+ more</p>
              </div>
              <div class="space-y-1">
                <p class="font-medium">Vue Components:</p>
                <div class="space-y-1">
                  <code class="bg-muted block px-1 text-xs rounded">
                    &lt;TryOutVoiceHype /&gt;
                  </code>
                  <code class="bg-muted block px-1 text-xs rounded">
                    &lt;Tip title="Pro Tip" content="Your helpful tip here" /&gt;
                  </code>
                  <code class="bg-muted block px-1 text-xs rounded">
                    &lt;SubscribeNewsletter /&gt;
                  </code>
                </div>
                <p class="text-muted-foreground">Use quotes for content with spaces</p>
              </div>
              <div class="space-y-1">
                <p class="font-medium">Shortcuts:</p>
                <div class="text-muted-foreground space-y-0.5">
                  <p>Ctrl+S: Save draft</p>
                  <p>Ctrl+Enter: Publish</p>
                  <p>Ctrl+/: Toggle preview</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<style scoped>
[contenteditable]:empty::before {
  content: attr(placeholder);
  color: #9ca3af;
  pointer-events: none;
}

.prose {
  outline: none;
}

.prose-invert {
  color: #e5e7eb;
}
</style>